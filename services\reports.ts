import { axiosInstance } from "@/lib/axios";
import { API_ROUTES } from "@/lib/api";
import { ApiReport, CreateReportData, UpdateReportData } from "@/types/report";

export class ReportsService {
  /**
   * Get all active reports
   */
  static async getActiveReports(): Promise<ApiReport[]> {
    const response = await axiosInstance.get(API_ROUTES.ACTIVE_REPORTS);

    return response.data;
  }

  /**
   * Get all reports (including inactive ones)
   */
  static async getAllReports(): Promise<ApiReport[]> {
    const response = await axiosInstance.get(API_ROUTES.ALL_REPORTS);

    return response.data;
  }

  /**
   * Get a single report by ID
   */
  static async getReport(id: number): Promise<ApiReport> {
    const response = await axiosInstance.get(`${API_ROUTES.GET_REPORT}${id}/`);

    return response.data;
  }

  /**
   * Create a new report
   */
  static async createReport(reportData: CreateReportData): Promise<ApiReport> {
    const response = await axiosInstance.post(
      API_ROUTES.CREATE_REPORT,
      reportData,
    );

    return response.data;
  }

  /**
   * Update an existing report
   */
  static async updateReport(
    id: number,
    updateData: UpdateReportData,
  ): Promise<ApiReport> {
    const response = await axiosInstance.put(
      `${API_ROUTES.UPDATE_REPORT}${id}/`,
      updateData,
    );

    return response.data;
  }

  /**
   * Delete a report
   */
  static async deleteReport(id: number): Promise<void> {
    await axiosInstance.delete(`${API_ROUTES.DELETE_REPORT}${id}/`);
  }

  /**
   * Toggle report active status
   */
  static async toggleReportStatus(id: number): Promise<ApiReport> {
    const response = await axiosInstance.post(
      `${API_ROUTES.TOGGLE_REPORT}${id}/`,
    );

    return response.data;
  }
}
