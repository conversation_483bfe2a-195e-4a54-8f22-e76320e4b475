import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON>dalHeader,
  ModalBody,
  <PERSON>dal<PERSON>ooter,
  Button,
  Form,
  Input,
  Select,
  SelectItem,
  Textarea,
} from "@heroui/react";
import { Icon } from "@iconify/react";

import { CreateTemplateModalProps, TemplateData } from "@/types/template";
import { useTemplates } from "@/hooks/templates/useTemplates";

export function CreateTemplateModal({
  isOpen,
  onClose,
  onConfirm,
  initialData,
  templates: initialTemplates,
}: CreateTemplateModalProps) {
  const {
    templates,
    loading: templatesLoading,
    fetchTemplates,
  } = useTemplates();
  const [formData, setFormData] = React.useState<TemplateData>({
    name: initialData?.name || "",
    description: initialData?.description || "",
    type: initialData?.type || "",
    id: initialData?.id || "",
  });

  const [nameExisits, setNameExists] = useState(false);

  React.useEffect(() => {
    if (initialData) {
      setFormData({
        name: initialData.name || "",
        description: initialData.description || "",
        type: initialData.id || initialData.type || "", // Use initialData.id as type if available
        id: initialData.id || "",
      });
    }
  }, [initialData]);

  useEffect(() => {
    if (formData.name && initialTemplates) {
      const nameExists = initialTemplates.some(
        (template) =>
          template.title.toLowerCase() ===
          formData.name.toLowerCase().trimEnd().trimStart(),
      );
      
      setNameExists(nameExists);
    } else {
      setNameExists(false);
    }
  }, [initialData, formData, initialTemplates]);

  React.useEffect(() => {
    if (isOpen) {
      fetchTemplates();
    }
  }, [isOpen]);

  const closeModal = () => {
    setFormData({ name: "", description: "", type: "" });
    onClose();
  };

  const handleTemplateSelection = (templateId: string) => {
    const selectedTemplate = templates.find(
      (template) => template.id.toString() === templateId,
    );

    if (selectedTemplate) {
      setFormData((prev) => ({
        ...prev,
        name: selectedTemplate.name,
        description: `Template ${selectedTemplate.name}`, // Default description since API doesn't provide it
        type: templateId,
      }));
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onConfirm(formData);
    setFormData({ name: "", description: "", type: "" });
    onClose();
  };

  return (
    <Modal backdrop="opaque" isOpen={isOpen} size="2xl" onClose={closeModal}>
      <ModalContent>
        {(closeModal) => (
          <Form className="w-full" onSubmit={handleSubmit}>
            <ModalHeader className="flex flex-col gap-1">
              <h2 className="text-xl font-semibold flex items-center gap-2">
                Crear plantilla
              </h2>
            </ModalHeader>
            <ModalBody className="w-full gap-4">
              <Input
                isRequired
                className="w-full"
                label="Nombre de la plantilla"
                labelPlacement="outside"
                placeholder="Insertar nombre de plantilla"
                startContent={
                  <Icon
                    className="text-default-400 pointer-events-none flex-shrink-0"
                    icon="lucide:text"
                  />
                }
                value={formData.name}
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, name: value }))
                }
              />

              <Textarea
                isRequired
                className="w-full"
                label="Descripción de la plantilla"
                labelPlacement="outside"
                placeholder="Insertar descripción de plantilla"
                startContent={
                  <Icon
                    className="text-default-400 pointer-events-none flex-shrink-0"
                    icon="lucide:align-left"
                  />
                }
                value={formData.description}
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, description: value }))
                }
              />

              <Select
                className="w-full"
                isLoading={templatesLoading}
                label="Clonar plantilla existente"
                labelPlacement="outside"
                placeholder="Seleccionar plantilla para clonar"
                selectedKeys={formData.type ? [formData.type] : []}
                onChange={(e) => {
                  const templateId = e.target.value;

                  if (templateId) {
                    handleTemplateSelection(templateId);
                  }
                }}
              >
                {templates.map((template) => (
                  <SelectItem key={template.id.toString()}>
                    {template.name}
                  </SelectItem>
                ))}
              </Select>
            </ModalBody>
            <ModalFooter className="w-full flex justify-between">
              <Button variant="flat" onPress={closeModal}>
                Cancelar
              </Button>
              <Button color="primary" isDisabled={nameExisits} type="submit">
                Crear
              </Button>
            </ModalFooter>
          </Form>
        )}
      </ModalContent>
    </Modal>
  );
}
