import React from "react";
import { Card } from "@heroui/react";

interface ProjectInfoProps {
  project: any | null;
}

export const ProjectInfo = ({ project }: ProjectInfoProps) => {
  const fields = [
    { label: "LID", value: project?.lid },
    { label: "Razón social", value: project?.companyName },
    { label: "Alias", value: project?.alias },
    { label: "Agregador", value: project?.aggregator },
    { label: "Tipología", value: project?.implementationType },
  ];

  return (
    <Card className="w-full bg-default-50 shadow-none border border-divider">
      <div className="grid grid-cols-5 gap-6 p-4">
        {fields.map((field, index) => (
          <div key={index} className="space-y-1">
            <h3 className="text-sm font-medium text-default-700">
              {field.label}
            </h3>
            <p className="text-sm text-default-500">{field.value || "-"}</p>
          </div>
        ))}
      </div>
    </Card>
  );
};
