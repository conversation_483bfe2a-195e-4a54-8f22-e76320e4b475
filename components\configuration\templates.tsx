"use client";

import React, { useEffect, useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dalFooter,
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { useRouter } from "next/navigation";
import { TbBulb, TbBulbOff } from "react-icons/tb";

import { CreateTemplateModal } from "./create-template-modal";
import { TemplateViewModal } from "./template-view-modal";
import { TemplateEditModal } from "./template-edit-modal";

import { TemplateData } from "@/types/template";
import { useTemplates } from "@/hooks/templates/useTemplates";

interface Template {
  id: number;
  title: string;
  description: string;
  is_active: boolean;
}

interface TemplatesProps {
  isCreating: boolean;
  setIsCreating: (value: boolean) => void;
  canEditConfiguration: boolean;
}

export default function Templates({
  isCreating,
  setIsCreating,
  canEditConfiguration,
}: TemplatesProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [clonedTemplate, setClonedTemplate] = useState<Template | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(
    null,
  );
  const [isDisableModalOpen, setIsDisableModalOpen] = useState(false);
  const [templateToDisable, setTemplateToDisable] = useState<Template | null>(
    null,
  );
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [templateToEdit, setTemplateToEdit] = useState<Template | null>(null);
  const router = useRouter();
  const {
    templates: apiTemplates,
    loading,
    error,
    fetchTemplates,
    disableTemplate,
  } = useTemplates();

  // Transform API data to match component interface
  const templateData: Template[] = apiTemplates.map((template) => ({
    id: template.id,
    title: template.name,
    description: template.description,
    is_active: template.is_active,
  }));

  const handleConfirm = (templateData: TemplateData) => {
    // Build the URL with template data
    const params = new URLSearchParams({
      title: templateData.name,
      description: templateData.description,
    });

    // If type contains a template ID (when cloning), add it as id parameter
    if (templateData.type) {
      params.append("id", templateData.type);
    }

    router.push(`/plantilla/crear?${params.toString()}`);
  };

  const handleCloneTemplate = (template: Template) => {
    setClonedTemplate(template);
    setIsOpen(true);
  };

  const handleViewTemplate = (template: Template) => {
    setSelectedTemplate(template);
    setIsViewModalOpen(true);
  };

  const handleModalClose = () => {
    setIsOpen(false);
    setClonedTemplate(null);
  };

  const handleViewModalClose = () => {
    setIsViewModalOpen(false);
    setSelectedTemplate(null);
  };

  const handleDisableTemplate = (template: Template) => {
    setTemplateToDisable(template);
    setIsDisableModalOpen(true);
  };

  const handleConfirmDisable = async () => {
    if (!templateToDisable) return;

    await disableTemplate(templateToDisable.id);
    setIsDisableModalOpen(false);
    setTemplateToDisable(null);
  };

  const handleDisableModalClose = () => {
    setIsDisableModalOpen(false);
    setTemplateToDisable(null);
  };

  const handleEditTemplate = (template: Template) => {
    setTemplateToEdit(template);
    setIsEditModalOpen(true);
  };

  const handleEditModalClose = () => {
    setIsEditModalOpen(false);
    setTemplateToEdit(null);
  };

  const handleSaveTemplate = async () => {
    // Refresh templates after save
    await fetchTemplates();
  };

  useEffect(() => {
    if (isCreating) {
      setIsOpen(true);
      setIsCreating(false);
    }
  }, [isCreating, setIsCreating]);

  useEffect(() => {
    if (isOpen) return;
    setClonedTemplate(null);
    setIsCreating(false);
  }, [isOpen]);

  useEffect(() => {
    fetchTemplates();
  }, []);
  
  // Render template content based on state

  let contentElement;

  if (loading) {
    contentElement = (
      <div className="pt-4 w-full flex justify-center items-center min-h-[200px]">
        <Spinner size="lg" />
      </div>
    );
  } else if (error) {
    contentElement = (
      <div className="pt-4 w-full flex justify-center items-center min-h-[200px]">
        <div className="text-center">
          <Icon
            className="text-4xl text-danger mb-2"
            icon="lucide:alert-circle"
          />
          <p className="text-danger">Error loading templates: {error}</p>
          <Button
            className="mt-2"
            color="primary"
            variant="flat"
            onPress={fetchTemplates}
          >
            Try Again
          </Button>
        </div>
      </div>
    );
  } else if (templateData.length === 0) {
    contentElement = (
      <div className="pt-4 w-full flex justify-center items-center min-h-[200px]">
        <div className="text-center">
          <Icon
            className="text-4xl text-default-400 mb-2"
            icon="lucide:folder-open"
          />
          <p className="text-default-500">No templates available</p>
        </div>
      </div>
    );
  } else {
    contentElement = (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {templateData
          .sort((a, b) => a.title.localeCompare(b.title))
          .sort((a, b) =>
            a.is_active === b.is_active ? 0 : a.is_active ? -1 : 1,
          )
          .map((template) => (
            <Card
              key={template.id}
              className="border border-default-200 cursor-pointer h-[185px] flex flex-col"
            >
              <CardBody className="p-0 flex-grow overflow-hidden">
                <div className="p-4 h-full flex flex-col">
                  <Tooltip content={template.title}>
                    <h3 className="text-large font-medium mb-2 line-clamp-1 overflow-hidden text-ellipsis">
                      {template.title}
                    </h3>
                  </Tooltip>
                  <Tooltip content={template.description}>
                    <p className="text-small text-default-500 line-clamp-3 overflow-hidden">
                      {template.description}
                    </p>
                  </Tooltip>
                </div>
              </CardBody>
              <CardFooter className="flex justify-between items-center px-4 py-3 border-t border-default-200 mt-auto">
                <Button
                  color="primary"
                  isDisabled={!canEditConfiguration}
                  size="sm"
                  startContent={<Icon className="text-lg" icon="lucide:copy" />}
                  variant="flat"
                  onPress={() => handleCloneTemplate(template)}
                >
                  Clonar plantilla
                </Button>
                <div>
                  <Button
                    isIconOnly
                    aria-label="Edit template"
                    color="primary"
                    isDisabled={!canEditConfiguration}
                    size="sm"
                    variant="light"
                    onPress={() => handleEditTemplate(template)}
                  >
                    <Icon className="text-lg" icon="lucide:edit-3" />
                  </Button>
                  <Button
                    isIconOnly
                    aria-label={
                      template.is_active
                        ? "Disable template"
                        : "Enable template"
                    }
                    color={!template.is_active ? "danger" : "success"}
                    size="sm"
                    variant="light"
                    onPress={() => handleDisableTemplate(template)}
                  >
                    {!template.is_active ? (
                      <TbBulbOff size={20} />
                    ) : (
                      <TbBulb size={20} />
                    )}
                  </Button>
                  <Button
                    isIconOnly
                    aria-label="Preview template"
                    size="sm"
                    variant="light"
                    onPress={() => handleViewTemplate(template)}
                  >
                    <Icon className="text-lg" icon="lucide:eye" />
                  </Button>
                </div>
              </CardFooter>
            </Card>
          ))}
      </div>
    );
  }

  return (
    <div className="pt-4 w-full">
      {contentElement}

      <CreateTemplateModal
        initialData={
          clonedTemplate
            ? {
                name: clonedTemplate.title,
                description: clonedTemplate.description,
                type: "",
                id: clonedTemplate.id.toString(),
              }
            : undefined
        }
        isOpen={isOpen}
        templates={templateData}
        onClose={handleModalClose}
        onConfirm={handleConfirm}
      />

      <TemplateViewModal
        isOpen={isViewModalOpen}
        template={selectedTemplate}
        onClose={handleViewModalClose}
      />

      <TemplateEditModal
        isOpen={isEditModalOpen}
        template={templateToEdit}
        onClose={handleEditModalClose}
        onSave={handleSaveTemplate}
      />

      {/* Disable Template Confirmation Modal */}
      <Modal
        backdrop="opaque"
        isOpen={isDisableModalOpen}
        size="md"
        onClose={handleDisableModalClose}
      >
        <ModalContent>
          <ModalHeader>
            <h3 className="text-xl font-bold">
              {templateToDisable?.is_active ? "Deshabilitar" : "Habilitar"}{" "}
              Plantilla
            </h3>
          </ModalHeader>
          <ModalBody>
            <p>
              {templateToDisable?.is_active ? (
                <>
                  ¿Está seguro de que desea deshabilitar la plantilla{" "}
                  <span className="font-semibold">
                    {templateToDisable?.title}
                  </span>
                  ?
                  <span className="block mt-2 text-gray-600 text-sm">
                    Esta plantilla ya no será seleccionable al crear un
                    proyecto. Esto no afecta los proyectos existentes.
                  </span>
                </>
              ) : (
                <>
                  ¿Está seguro de que desea habilitar la plantilla{" "}
                  <span className="font-semibold">
                    {templateToDisable?.title}
                  </span>
                  ?
                  <span className="block mt-2 text-gray-600 text-sm">
                    Esta plantilla estará disponible para crear nuevos
                    proyectos.
                  </span>
                </>
              )}
            </p>
          </ModalBody>
          <ModalFooter>
            <Button
              color="default"
              variant="flat"
              onPress={handleDisableModalClose}
            >
              Cancelar
            </Button>
            <Button
              color={templateToDisable?.is_active ? "danger" : "success"}
              isLoading={loading}
              onPress={handleConfirmDisable}
            >
              {templateToDisable?.is_active ? "Deshabilitar" : "Habilitar"}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
}
